<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Emums\ItemSize;
use App\Emums\OrderItemType;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = ['order_id', 'item_type', 'color', 'size', 'quantity', 'unit_price'];

    protected $casts = [
        'size' => ItemSize::class,
        'item_type' => OrderItemType::class,
    ];

    public function job(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function itemTasks(): HasMany
    {
        return $this->hasMany(OrderTask::class);
    }

    public function description(): Attribute
    {
        return Attribute::get(function () {
            $quantity = number_format($this->quantity);
            $color = ucfirst($this->color);
            $typeName = $this->item_type->getLabel();
            $sizeLabel = $this->size?->getLabel();

            return "{$quantity} {$color} {$typeName} ({$sizeLabel})";
        });
    }

    public function totalPrice(): Attribute
    {
        return Attribute::get(function () {
            $quantity = $this->quantity;
            $unit_price = $this->unit_price;

            return $quantity * $unit_price;
        });
    }
}
