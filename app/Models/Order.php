<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;


class Order extends Model
{
    use HasFactory;

    protected $fillable = ['client_id', 'order_number', 'start_date', 'end_date', 'discount', 'vat', 'status'];

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(OrderTask::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function jobValue(): Attribute
    {
        return Attribute::get(
            fn () => $this->items->sum('total_price')
        );
    }

    public function uniqueItemTypes()
    {
        return $this->items->pluck('item_type')->unique();
    }

}
