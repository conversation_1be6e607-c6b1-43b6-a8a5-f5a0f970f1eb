<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained();  
            $table->foreignId('order_item_id')->nullable()->constrained();
            $table->string('task_type');
            $table->string('description')->nullable();
            $table->decimal('rate_per_unit', 10, 2);
            $table->foreignId('parent_task_id')->nullable()->constrained('order_tasks');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_tasks');
    }
};
