<?php

namespace App\Emums;

use Filament\Support\Contracts\HasLabel;

enum OrderItemType: string implements <PERSON><PERSON>abe<PERSON>
{
    case LONG_SLEEVED_SHIRT = 'long-sleeved-shirt';
    case SHORT_SLEEVED_SHIRT = 'short-sleeved-shirt';
    case TROUSER = 'trouser';
    case OVERALL = 'overall';

    public function getLabel(): string
    {
        return match ($this) {
            self::LONG_SLEEVED_SHIRT => 'Long Sleeved Shirt',
            self::SHORT_SLEEVED_SHIRT => 'Short Sleeved Shirt',
            self::TROUSER => 'Trouser',
            self::OVERALL => 'Overall',
        };
    }
}
