<?php

namespace App\Emums;

use Filament\Support\Contracts\HasLabel;

enum ItemSize: string implements HasLabel
{
    case S = 'small';
    case M = 'medium';
    case L = 'large';
    case XL = 'extra-large';
    case XXL = 'double-extra-large';

    public function getLabel(): string
    {
        return match ($this) {
            self::S => 'Small',
            self::M => 'Medium',
            self::L => 'Large',
            self::XL => 'Extra Large',
            self::XXL => 'Double Extra Large'
        };
    }
}
