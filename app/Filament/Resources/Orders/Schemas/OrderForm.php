<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Repeater\TableColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use App\Emums\OrderItemType;
use App\Emums\ItemSize;
use Filament\Schemas\Schema;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('client_id')
                    ->relationship('client', 'name')
                    ->required(),
                TextInput::make('order_number')
                    ->required(),
                DatePicker::make('start_date')
                    ->required(),
                DatePicker::make('end_date'),
                Repeater::make('items')->relationship()
                    ->table([
                        TableColumn::make('Item Type'),
                        TableColumn::make('Size'),
                        TableColumn::make('Color'),
                        TableColumn::make('Unit Price'),
                        TableColumn::make('Quantity'),
                    ])
                    ->schema([
                        Select::make('item_type')
                            ->options(OrderItemType::class)
                            ->required(),
                        Select::make('size')
                            ->options(ItemSize::class)
                            ->required(),
                        TextInput::make('color'),
                        TextInput::make('unit_price')
                            ->numeric()
                            ->prefix('₦')
                            ->required(),
                        TextInput::make('quantity')
                            ->numeric()
                            ->required(),
                        ])->columnSpanFull(),
            ]);
    }
}
