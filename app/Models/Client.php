<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'address', 'contact_person', 'phone', 'email'];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
