<?php

namespace App\Emums;

use Filament\Support\Contracts\HasLabel;

enum OrderTaskType: string implements Has<PERSON>abel
{
    case CUTTING = 'cutting';
    case JOINING = 'joining';
    case BUTTON_HOLES = 'button-holes';
    case BUTTON_TACKING = 'button-tacking';
    case ROPES = 'ropes';
    case IRONING = 'ironing';
    case PACKING = 'packing';

    public function getLabel(): string
    {
        return match ($this) {
            self::CUTTING => 'Cutting',
            self::JOINING => 'Joining',
            self::BUTTON_HOLES => 'Button Holes',
            self::BUTTON_TACKING => 'Button Tacking',
            self::ROPES => 'Ropes',
            self::IRONING => 'Ironing',
            self::PACKING => 'Packing',
        };
    }
}
